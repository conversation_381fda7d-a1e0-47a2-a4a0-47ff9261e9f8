export const useSEO = () => {
  const route = useRoute()
  const baseUrl = 'https://geminigen.ai'

  /**
   * Set canonical URL for the current page
   */
  const setCanonicalUrl = (customPath?: string) => {
    const canonicalPath = customPath || route.path
    const canonicalUrl = `${baseUrl}${canonicalPath}`
    
    useHead({
      link: [
        {
          rel: 'canonical',
          href: canonicalUrl
        }
      ]
    })
  }

  /**
   * Set hreflang tags for multi-language support
   */
  const setHreflangTags = (languages: string[] = ['en', 'vi']) => {
    const currentPath = route.path
    const hreflangLinks = languages.map(lang => ({
      rel: 'alternate',
      hreflang: lang,
      href: `${baseUrl}${currentPath}?lang=${lang}`
    }))

    // Add x-default
    hreflangLinks.push({
      rel: 'alternate',
      hreflang: 'x-default',
      href: `${baseUrl}${currentPath}`
    })

    useHead({
      link: hreflangLinks
    })
  }

  /**
   * Set Open Graph image
   */
  const setOgImage = (imagePath: string) => {
    const imageUrl = imagePath.startsWith('http') 
      ? imagePath 
      : `${baseUrl}${imagePath}`

    useSeoMeta({
      ogImage: imageUrl,
      twitterImage: imageUrl
    })
  }

  /**
   * Set breadcrumb structured data
   */
  const setBreadcrumbs = (breadcrumbs: Array<{ name: string; url: string }>) => {
    const breadcrumbList = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': item.name,
        'item': `${baseUrl}${item.url}`
      }))
    }

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(breadcrumbList)
        }
      ]
    })
  }

  /**
   * Set FAQ structured data
   */
  const setFAQStructuredData = (faqs: Array<{ question: string; answer: string }>) => {
    const faqStructuredData = {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      'mainEntity': faqs.map(faq => ({
        '@type': 'Question',
        'name': faq.question,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': faq.answer
        }
      }))
    }

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(faqStructuredData)
        }
      ]
    })
  }

  /**
   * Set article structured data
   */
  const setArticleStructuredData = (article: {
    title: string
    description: string
    author: string
    datePublished: string
    dateModified?: string
    image?: string
  }) => {
    const articleStructuredData = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      'headline': article.title,
      'description': article.description,
      'author': {
        '@type': 'Person',
        'name': article.author
      },
      'publisher': {
        '@type': 'Organization',
        'name': 'GeminiGen AI',
        'logo': {
          '@type': 'ImageObject',
          'url': `${baseUrl}/logo.png`
        }
      },
      'datePublished': article.datePublished,
      'dateModified': article.dateModified || article.datePublished,
      'image': article.image ? `${baseUrl}${article.image}` : `${baseUrl}/og-image.png`
    }

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(articleStructuredData)
        }
      ]
    })
  }

  return {
    setCanonicalUrl,
    setHreflangTags,
    setOgImage,
    setBreadcrumbs,
    setFAQStructuredData,
    setArticleStructuredData
  }
}
