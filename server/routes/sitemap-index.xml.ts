export default defineEventHandler(async (event) => {
  const baseUrl = 'https://geminigen.ai'
  const currentDate = new Date().toISOString()

  // Define sitemap files
  const sitemaps = [
    {
      url: `${baseUrl}/sitemap.xml`,
      lastmod: currentDate
    }
    // Add more sitemaps here if needed (e.g., blog sitemap, product sitemap)
    // {
    //   url: `${baseUrl}/sitemap-blog.xml`,
    //   lastmod: currentDate
    // }
  ]

  // Generate XML sitemap index
  const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps.map(sitemap => `  <sitemap>
    <loc>${sitemap.url}</loc>
    <lastmod>${sitemap.lastmod}</lastmod>
  </sitemap>`).join('\n')}
</sitemapindex>`

  // Set proper headers
  setHeader(event, 'Content-Type', 'application/xml')
  setHeader(event, 'Cache-Control', 'max-age=3600') // Cache for 1 hour

  return sitemapIndex
})
