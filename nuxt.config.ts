// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  modules: [
    '@nuxt/eslint',
    '@nuxt/image',
    '@nuxt/ui-pro',
    '@vueuse/nuxt',
    'motion-v/nuxt',
    '@nuxtjs/i18n',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    'nuxt-vue3-google-signin',
    '@nuxtjs/supabase',
    '@nuxt/content'
  ],
  plugins: [{ src: '~/plugins/vue-number-animation', ssr: false }],
  ssr: false,
  components: [
    {
      path: '~/components',
      pathPrefix: false
    }
  ],
  devtools: {
    enabled: true
  },
  app: {
    rootAttrs: {
      'data-vaul-drawer-wrapper': ''
    },
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'GeminiGen AI',
      meta: [
        {
          name: 'description',
          content: 'GeminiGen AI'
        }
      ],
      script: [
        {
          src: 'https://www.googletagmanager.com/gtag/js?id=AW-17097171976',
          async: true
        },
        {
          innerHTML: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'AW-17097171976');
          `,
          tagPosition: 'head'
        },
        {
          innerHTML: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                  })(window,document,'script','dataLayer','${process.env.NUXT_GOOGLE_ANALYTICS_ID}');`,
          tagPosition: 'head'
        }
      ],
      noscript: [
        {
          innerHTML: `<iframe src="https://www.googletagmanager.com/ns.html?id=${process.env.NUXT_GOOGLE_ANALYTICS_ID}"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
          tagPosition: 'bodyOpen'
        }
      ]
    }
  },

  css: ['~/assets/css/main.css', 'animate.css/animate.min.css'],

  // Color mode configuration for proper theme persistence with SSR disabled
  colorMode: {
    preference: 'system', // default value of $colorMode.preference
    fallback: 'light', // fallback value if no system preference found
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '-mode',
    storageKey: 'nuxt-color-mode',
    storage: 'localStorage', // or 'sessionStorage' or 'cookie'
    disableTransition: false // enable transition when switching themes
  },
  content: {
    experimental: { nativeSqlite: true }
  },

  runtimeConfig: {
    public: {
      api: {
        imagenproService:
          process.env.IMAGENPRO_SERVICE_BASE_URL
          || 'https://api-dev.geminigen.ai/api'
      },
      NUXT_GOOGLE_CLIENT_ID:
        process.env.NUXT_GOOGLE_CLIENT_ID
        || '68752252893-mernhuth6qublvpb7o5enb2tt9jeptsu.apps.googleusercontent.com',
      NUXT_PUBLIC_SUPABASE_URL:
        process.env.NUXT_PUBLIC_SUPABASE_URL
        || 'https://realtime-dev.ttsopenai.com',
      NUXT_PUBLIC_SUPABASE_ANON_KEY:
        process.env.NUXT_PUBLIC_SUPABASE_ANON_KEY
        || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzI0MDAwNDAwLAogICJleHAiOiA0MDkwODMyNDQzCn0.2jwTXghgcYZRJz38_dDpIPIhRfHq1ZtgRHAHkxHpBcg',
      NUXT_NOTIFICATION_TABLE:
        process.env.NUXT_NOTIFICATION_TABLE || 'notifications_geminigen_dev',
      features: {
        paymentWithCrypto: process.env.FEATURE_PAYMENT_WITH_CRYPTO === 'true',
        beta: process.env.FEATURE_BETA === 'true',
        serverSelection: process.env.FEATURE_SERVER_SELECTION === 'true'
      } as any,
      NUXT_PAYPAL_ID:
        process.env.NUXT_PAYPAL_ID
        || 'AW-WZ2ivnIbO_8IMhT0rui4HXF0oAfm5Yfhy165gG2j9yZDnwfUFcYzowTc42IJhB3YSzKDPU9LitDXv',
      resourceBucket:
        process.env.NUXT_RESOURCE_BUCKET
        || 'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/'
    }
  },
  build: { transpile: ['vue-number-animation'] },
  routeRules: {
    // Landing page will be served at root, app at /app
    '/app': { redirect: '/app/video-gen' }
  },

  future: {
    compatibilityVersion: 4
  },

  compatibilityDate: '2024-11-01',

  nitro: {
    prerender: {
      routes: [
        '/',
        '/app',
        '/app/video-gen',
        '/app/speech-gen',
        '/app/dialogue-gen',
        '/pricing',
        '/about',
        '/auth/login',
        '/auth/signup',
        '/terms',
        '/privacy',
        '/blog',
        '/api-docs'
      ],
      crawlLinks: true
    }
  },

  eslint: {
    config: {
      stylistic: {
        commaDangle: 'never',
        braceStyle: '1tbs'
      }
    }
  },

  googleSignIn: {
    clientId:
      process.env.NUXT_GOOGLE_CLIENT_ID
      || '68752252893-mernhuth6qublvpb7o5enb2tt9jeptsu.apps.googleusercontent.com'
  },
  i18n: {
    defaultLocale: 'vi',
    locales: [
      { code: 'en', name: 'English', file: 'en.json' },
      { code: 'vi', name: 'Tiếng Việt', file: 'vi.json' },
      { code: 'zh_cn', name: '简体中文', file: 'zh.json' },
      { code: 'ja', name: '日本語', file: 'ja.json' },
      { code: 'es', name: 'Español', file: 'es.json' },
      { code: 'fr', name: 'Français', file: 'fr.json' },
      { code: 'de', name: 'Deutsch', file: 'de.json' },
      { code: 'pt', name: 'Português', file: 'pt.json' }
    ],
    strategy: 'no_prefix'
  },
  supabase: {
    redirect: false,
    url:
      process.env.NUXT_PUBLIC_SUPABASE_URL
      || 'https://realtime-dev.ttsopenai.com',
    key:
      process.env.NUXT_PUBLIC_SUPABASE_ANON_KEY
      || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzI0MDAwNDAwLAogICJleHAiOiA0MDkwODMyNDQzCn0.2jwTXghgcYZRJz38_dDpIPIhRfHq1ZtgRHAHkxHpBcg'
  }
})
